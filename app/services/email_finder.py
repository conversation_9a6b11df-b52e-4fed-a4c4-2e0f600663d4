import requests
from bs4 import BeautifulSoup
import re
import time
from urllib.parse import quote
from config import Config

class EmailFinder:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
    
    def find_hr_emails(self, company_name, company_domain=None):
        """Find HR professional emails for a company"""
        results = []
        
        # Try different methods
        methods = [
            self._try_hunter_api,
            self._try_google_search,
            self._try_linkedin_search,
            self._try_common_patterns
        ]
        
        for method in methods:
            try:
                method_results = method(company_name, company_domain)
                if method_results:
                    results.extend(method_results)
            except Exception as e:
                print(f"Error in {method.__name__}: {e}")
                continue
        
        # Remove duplicates and return top results
        unique_results = []
        seen_emails = set()
        
        for result in results:
            if result['email'] not in seen_emails:
                unique_results.append(result)
                seen_emails.add(result['email'])
        
        return unique_results[:5]  # Return top 5 results
    
    def _try_hunter_api(self, company_name, company_domain):
        """Try Hunter.io API to find emails"""
        api_key = Config.HUNTER_API_KEY
        if not api_key or not company_domain:
            return []
        
        url = "https://api.hunter.io/v2/domain-search"
        params = {
            'domain': company_domain,
            'api_key': api_key,
            'type': 'personal',
            'limit': 10
        }
        
        response = self.session.get(url, params=params)
        if response.status_code == 200:
            data = response.json()
            emails = data.get('data', {}).get('emails', [])
            
            results = []
            for email_data in emails:
                if self._is_hr_related(email_data.get('first_name', ''), 
                                     email_data.get('last_name', ''), 
                                     email_data.get('position', '')):
                    results.append({
                        'name': f"{email_data.get('first_name', '')} {email_data.get('last_name', '')}".strip(),
                        'email': email_data.get('value', ''),
                        'position': email_data.get('position', ''),
                        'source': 'Hunter.io',
                        'confidence': email_data.get('confidence', 0)
                    })
            
            return results
        
        return []
    
    def _try_google_search(self, company_name, company_domain):
        """Search Google for HR emails"""
        results = []
        
        # Different search queries
        queries = [
            f'"{company_name}" HR email contact',
            f'"{company_name}" human resources email',
            f'"{company_name}" recruiting email',
            f'site:{company_domain} HR email' if company_domain else None,
            f'site:linkedin.com "{company_name}" HR manager'
        ]
        
        for query in queries:
            if not query:
                continue
                
            try:
                search_results = self._google_search(query)
                for result in search_results:
                    emails = self._extract_emails_from_text(result.get('snippet', ''))
                    for email in emails:
                        if company_domain and company_domain in email:
                            results.append({
                                'name': self._extract_name_from_context(result.get('snippet', ''), email),
                                'email': email,
                                'position': 'HR Professional',
                                'source': 'Google Search',
                                'confidence': 70
                            })
                
                time.sleep(1)  # Be respectful
            except Exception as e:
                print(f"Error in Google search: {e}")
                continue
        
        return results
    
    def _try_linkedin_search(self, company_name, company_domain):
        """Search for LinkedIn profiles (limited without API)"""
        # Note: This is a basic implementation. LinkedIn has strict anti-scraping measures.
        # In production, you'd want to use LinkedIn's official API or a service like Apollo.io
        
        results = []
        try:
            query = f'site:linkedin.com "{company_name}" "HR" OR "Human Resources" OR "Recruiting"'
            search_results = self._google_search(query)
            
            for result in search_results[:3]:  # Limit to avoid rate limiting
                linkedin_url = result.get('link', '')
                if 'linkedin.com/in/' in linkedin_url:
                    # Extract basic info from search snippet
                    snippet = result.get('snippet', '')
                    name = self._extract_name_from_linkedin_snippet(snippet)
                    
                    if name:
                        # Try to guess email based on common patterns
                        if company_domain:
                            guessed_emails = self._guess_email_patterns(name, company_domain)
                            for email in guessed_emails:
                                results.append({
                                    'name': name,
                                    'email': email,
                                    'position': 'HR Professional (LinkedIn)',
                                    'source': 'LinkedIn + Email Pattern',
                                    'confidence': 50,
                                    'linkedin_url': linkedin_url
                                })
        except Exception as e:
            print(f"Error in LinkedIn search: {e}")
        
        return results
    
    def _try_common_patterns(self, company_name, company_domain):
        """Try common HR email patterns"""
        if not company_domain:
            return []
        
        common_hr_emails = [
            f'hr@{company_domain}',
            f'humanresources@{company_domain}',
            f'recruiting@{company_domain}',
            f'careers@{company_domain}',
            f'jobs@{company_domain}',
            f'talent@{company_domain}'
        ]
        
        results = []
        for email in common_hr_emails:
            if self._verify_email_exists(email):
                results.append({
                    'name': 'HR Department',
                    'email': email,
                    'position': 'HR Department',
                    'source': 'Common Pattern',
                    'confidence': 60
                })
        
        return results
    
    def _google_search(self, query):
        """Perform Google search"""
        api_key = Config.GOOGLE_API_KEY
        cse_id = Config.GOOGLE_CSE_ID
        
        if api_key and cse_id:
            # Use Google Custom Search API
            url = "https://www.googleapis.com/customsearch/v1"
            params = {
                'key': api_key,
                'cx': cse_id,
                'q': query,
                'num': 5
            }
            
            response = self.session.get(url, params=params)
            if response.status_code == 200:
                data = response.json()
                return data.get('items', [])
        
        # Fallback to scraping (less reliable)
        return self._scrape_google_search(query)
    
    def _scrape_google_search(self, query):
        """Scrape Google search results (fallback)"""
        try:
            url = f"https://www.google.com/search?q={quote(query)}"
            response = self.session.get(url)
            
            if response.status_code != 200:
                return []
            
            soup = BeautifulSoup(response.content, 'html.parser')
            results = []
            
            # Extract search results
            for result in soup.find_all('div', class_='g')[:5]:
                title_elem = result.find('h3')
                link_elem = result.find('a', href=True)
                snippet_elem = result.find('span', class_='aCOpRe')
                
                if title_elem and link_elem:
                    results.append({
                        'title': title_elem.get_text(),
                        'link': link_elem['href'],
                        'snippet': snippet_elem.get_text() if snippet_elem else ''
                    })
            
            return results
        except Exception:
            return []
    
    def _extract_emails_from_text(self, text):
        """Extract email addresses from text"""
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        return re.findall(email_pattern, text)
    
    def _extract_name_from_context(self, text, email):
        """Try to extract name from context around email"""
        # Simple implementation - look for capitalized words near email
        words = text.split()
        email_index = -1
        
        for i, word in enumerate(words):
            if email in word:
                email_index = i
                break
        
        if email_index > 0:
            # Look for names before email
            for i in range(max(0, email_index - 5), email_index):
                word = words[i]
                if word[0].isupper() and len(word) > 2:
                    next_word = words[i + 1] if i + 1 < len(words) else ""
                    if next_word and next_word[0].isupper():
                        return f"{word} {next_word}"
                    return word
        
        return "HR Professional"
    
    def _extract_name_from_linkedin_snippet(self, snippet):
        """Extract name from LinkedIn search snippet"""
        # Look for patterns like "John Doe - HR Manager at Company"
        patterns = [
            r'^([A-Z][a-z]+ [A-Z][a-z]+)',
            r'([A-Z][a-z]+ [A-Z][a-z]+) -',
            r'([A-Z][a-z]+ [A-Z][a-z]+) \|'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, snippet)
            if match:
                return match.group(1)
        
        return None
    
    def _guess_email_patterns(self, name, domain):
        """Guess email patterns based on name and domain"""
        if not name or ' ' not in name:
            return []
        
        parts = name.lower().split()
        first_name = parts[0]
        last_name = parts[-1]
        
        patterns = [
            f"{first_name}.{last_name}@{domain}",
            f"{first_name}@{domain}",
            f"{last_name}@{domain}",
            f"{first_name[0]}{last_name}@{domain}",
            f"{first_name}{last_name[0]}@{domain}",
            f"{first_name}_{last_name}@{domain}"
        ]
        
        return patterns
    
    def _is_hr_related(self, first_name, last_name, position):
        """Check if person is HR-related based on position"""
        if not position:
            return False
        
        hr_keywords = [
            'hr', 'human resources', 'recruiting', 'recruiter', 'talent',
            'people', 'personnel', 'hiring', 'staffing'
        ]
        
        position_lower = position.lower()
        return any(keyword in position_lower for keyword in hr_keywords)
    
    def _verify_email_exists(self, email):
        """Basic email verification (simplified)"""
        # In production, you'd use a proper email verification service
        # This is a basic check for common patterns
        return '@' in email and '.' in email.split('@')[1]
